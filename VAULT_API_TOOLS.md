# Autodesk Vault API Tools for MCP System

## Overview

Your MCP system now includes comprehensive Autodesk Vault API tools that allow you to interact with Vault data programmatically. These tools use the correct Vault Data API v2 endpoints and follow the official API structure.

## Base URL Structure

The tools automatically construct the correct Vault API base URL:
```
{VaultServerAddress}/AutodeskDM/Services/api/vault/v2
```

## Available Vault Tools

### 1. Authentication & Setup

#### `vaultAuthenticate`
Authenticate with Autodesk Vault and get available vaults.

**Parameters:**
- `accessToken` (required): OAuth access token from Autodesk Platform Services
- `vaultServer` (required): Vault server URL (e.g., https://your-vault-server.com)

**Example Usage:**
```bash
curl -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "Authenticate with Vault server at https://vault.company.com using token abc123"}'
```

### 2. File Operations

#### `vaultGetFileVersions`
Get file versions from Autodesk Vault with search and filtering capabilities.

**Parameters:**
- `accessToken` (required): OAuth access token
- `vaultServer` (required): Vault server URL
- `vaultId` (required): Vault ID to search in
- `searchTerm` (optional): Search term for file names/properties
- `limit` (optional): Max results (default: 50)

**Returns:** Paginated results with file versions, included folder data, and metadata.

#### `vaultGetFileById`
Get a specific file by ID.

#### `vaultGetFileVersionById`
Get a specific file version by ID.

#### `vaultSearchFiles`
Advanced file search with extension filtering and folder scoping.

**Parameters:**
- `searchTerm` (required): Search term
- `folderId` (optional): Limit to specific folder
- `fileExtension` (optional): Filter by extension (.dwg, .ipt, .iam)
- `limit` (optional): Max results

### 3. Folder Operations

#### `vaultGetFolders`
Get folders from Autodesk Vault with hierarchical navigation.

**Parameters:**
- `parentFolderId` (optional): Parent folder ID for navigation
- `searchTerm` (optional): Filter folders by name

### 4. Item Operations

#### `vaultGetItems`
Get items from Autodesk Vault.

#### `vaultGetItemById`
Get a specific item by ID.

#### `vaultGetItemHistory`
Get version history for an item.

### 5. Search Operations

#### `vaultBasicSearch`
Perform basic search across files, folders, and items.

**Parameters:**
- `searchTerm` (required): What to search for
- `searchType` (optional): files, folders, items, or all
- `searchProperties` (optional): Include properties in search

### 6. Workflow Operations

#### `vaultGetChangeOrders`
Get change orders with status filtering.

#### `vaultGetJobs`
Get jobs with status filtering.

## Multi-Step Planning Examples

Your MCP system can now handle complex Vault workflows:

### Example 1: Find CAD Files in Project
**Question:** "Find all Inventor assembly files (.iam) in the ProjectX folder"

**AI Planning:**
1. Authenticate with Vault
2. Search for folders named "ProjectX"
3. Search for .iam files in that folder
4. Return results with file details

### Example 2: Check File Status
**Question:** "Is the MainAssembly.iam file checked out and by whom?"

**AI Planning:**
1. Search for "MainAssembly.iam"
2. Get file version details
3. Check checkout status and user
4. Provide status report

### Example 3: Project Analysis
**Question:** "How many files were modified in the last week in vault 117?"

**AI Planning:**
1. Get all file versions from vault 117
2. Filter by modification date
3. Count and categorize results
4. Generate summary report

## Response Structure

All Vault tools return structured data following the official API format:

```json
{
  "pagination": {
    "limit": 50,
    "totalResults": 150,
    "nextUrl": "...",
    "indexingStatus": "IndexingComplete"
  },
  "results": [...],
  "included": {
    "folder": {...},
    "file": {...}
  }
}
```

## Error Handling

The tools provide comprehensive error handling:
- Authentication failures
- Invalid vault IDs
- Network connectivity issues
- API rate limiting
- Malformed requests

## Integration with Multi-Step Planning

The Vault tools work seamlessly with your multi-step planning system:

1. **Information Gathering**: Tools can discover vault structure
2. **Iterative Queries**: Results from one tool inform the next
3. **Context Building**: Maintains vault session across steps
4. **Natural Responses**: Converts technical data to user-friendly answers

## Security Notes

- Always use HTTPS for production Vault servers
- OAuth tokens should be kept secure
- Consider token refresh for long-running operations
- Vault permissions are enforced by the server

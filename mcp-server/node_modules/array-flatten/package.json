{"name": "array-flatten", "version": "1.1.1", "description": "Flatten an array of nested arrays into a single flat array", "main": "array-flatten.js", "files": ["array-flatten.js", "LICENSE"], "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/array-flatten.git"}, "keywords": ["array", "flatten", "arguments", "depth"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/array-flatten/issues"}, "homepage": "https://github.com/blakeembrey/array-flatten", "devDependencies": {"istanbul": "^0.3.13", "mocha": "^2.2.4", "pre-commit": "^1.0.7", "standard": "^3.7.3"}}
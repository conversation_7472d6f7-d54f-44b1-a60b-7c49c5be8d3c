const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

const toolsRegistry = new Map();

function registerTool(name, description, params, handler) {
  toolsRegistry.set(name, {
    name,
    description,
    params,
    handler
  });
}

app.get('/tools', (req, res) => {
  const tools = Array.from(toolsRegistry.values()).map(tool => ({
    name: tool.name,
    description: tool.description,
    params: tool.params
  }));
  
  res.json({ tools });
});

app.post('/tool', async (req, res) => {
  try {
    const { toolName, params = {} } = req.body;
    
    if (!toolsRegistry.has(toolName)) {
      return res.status(404).json({ 
        error: `Tool '${toolName}' not found`,
        availableTools: Array.from(toolsRegistry.keys())
      });
    }
    
    const tool = toolsRegistry.get(toolName);
    
    const requiredParams = tool.params.filter(p => p.required);
    for (const param of requiredParams) {
      if (!(param.name in params)) {
        return res.status(400).json({
          error: `Missing required parameter: ${param.name}`,
          description: param.description
        });
      }
    }
    
    const result = await tool.handler(params);
    res.json({ result, toolName, params });
    
  } catch (error) {
    res.status(500).json({ 
      error: error.message,
      toolName: req.body.toolName 
    });
  }
});

registerTool(
  'getTime',
  'Get current date and time',
  [],
  async () => {
    return {
      timestamp: Date.now(),
      date: new Date().toISOString(),
      formatted: new Date().toLocaleString()
    };
  }
);

registerTool(
  'listFiles',
  'List files in a directory',
  [
    { name: 'directory', required: true, description: 'Directory path to list files from' },
    { name: 'includeHidden', required: false, description: 'Include hidden files (default: false)' }
  ],
  async (params) => {
    const { directory, includeHidden = false } = params;
    
    if (!await fs.pathExists(directory)) {
      throw new Error(`Directory '${directory}' does not exist`);
    }
    
    const files = await fs.readdir(directory);
    const filteredFiles = includeHidden ? files : files.filter(f => !f.startsWith('.'));
    
    const fileDetails = await Promise.all(
      filteredFiles.map(async (file) => {
        const filePath = path.join(directory, file);
        const stats = await fs.stat(filePath);
        return {
          name: file,
          path: filePath,
          isDirectory: stats.isDirectory(),
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        };
      })
    );
    
    return { directory, files: fileDetails };
  }
);

registerTool(
  'getFileInfo',
  'Get detailed information about a file or directory',
  [
    { name: 'filePath', required: true, description: 'Path to the file or directory' }
  ],
  async (params) => {
    const { filePath } = params;
    
    if (!await fs.pathExists(filePath)) {
      throw new Error(`Path '${filePath}' does not exist`);
    }
    
    const stats = await fs.stat(filePath);
    const absolutePath = path.resolve(filePath);
    
    return {
      path: filePath,
      absolutePath,
      name: path.basename(filePath),
      isDirectory: stats.isDirectory(),
      isFile: stats.isFile(),
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      accessed: stats.atime
    };
  }
);

registerTool(
  'calculateDaysSince',
  'Calculate days between a date and now',
  [
    { name: 'date', required: true, description: 'Date to calculate from (ISO string or timestamp)' }
  ],
  async (params) => {
    const { date } = params;
    const targetDate = new Date(date);
    const now = new Date();

    if (isNaN(targetDate.getTime())) {
      throw new Error('Invalid date format');
    }

    const diffTime = Math.abs(now - targetDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return {
      targetDate: targetDate.toISOString(),
      currentDate: now.toISOString(),
      daysDifference: diffDays,
      wasInPast: targetDate < now
    };
  }
);

registerTool(
  'searchFiles',
  'Search for files by name pattern in a directory',
  [
    { name: 'directory', required: true, description: 'Directory to search in' },
    { name: 'pattern', required: true, description: 'File name pattern to search for (supports wildcards)' },
    { name: 'recursive', required: false, description: 'Search recursively in subdirectories (default: false)' }
  ],
  async (params) => {
    const { directory, pattern, recursive = false } = params;

    if (!await fs.pathExists(directory)) {
      throw new Error(`Directory '${directory}' does not exist`);
    }

    const searchInDir = async (dir, depth = 0) => {
      const files = await fs.readdir(dir);
      let results = [];

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = await fs.stat(filePath);

        if (stats.isDirectory() && recursive) {
          results = results.concat(await searchInDir(filePath, depth + 1));
        } else if (stats.isFile()) {
          const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');
          if (regex.test(file)) {
            results.push({
              name: file,
              path: filePath,
              directory: dir,
              size: stats.size,
              created: stats.birthtime,
              modified: stats.mtime
            });
          }
        }
      }

      return results;
    };

    const results = await searchInDir(directory);
    return { searchPattern: pattern, directory, recursive, matches: results };
  }
);

registerTool(
  'readFile',
  'Read the contents of a text file',
  [
    { name: 'filePath', required: true, description: 'Path to the file to read' },
    { name: 'encoding', required: false, description: 'File encoding (default: utf8)' }
  ],
  async (params) => {
    const { filePath, encoding = 'utf8' } = params;

    if (!await fs.pathExists(filePath)) {
      throw new Error(`File '${filePath}' does not exist`);
    }

    const stats = await fs.stat(filePath);
    if (!stats.isFile()) {
      throw new Error(`'${filePath}' is not a file`);
    }

    const content = await fs.readFile(filePath, encoding);
    return {
      filePath,
      encoding,
      size: stats.size,
      content,
      lineCount: content.split('\n').length
    };
  }
);

registerTool(
  'createDirectory',
  'Create a new directory',
  [
    { name: 'directoryPath', required: true, description: 'Path for the new directory' },
    { name: 'recursive', required: false, description: 'Create parent directories if they don\'t exist (default: true)' }
  ],
  async (params) => {
    const { directoryPath, recursive = true } = params;

    if (await fs.pathExists(directoryPath)) {
      throw new Error(`Directory '${directoryPath}' already exists`);
    }

    await fs.ensureDir(directoryPath);

    return {
      directoryPath,
      created: true,
      absolutePath: path.resolve(directoryPath)
    };
  }
);

registerTool(
  'calculateDaysFromCurrentMonth',
  'Calculate days between a day in the current month and today',
  [
    { name: 'day', required: true, description: 'Day of the current month (1-31)' }
  ],
  async (params) => {
    const { day } = params;
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    const targetDate = new Date(currentYear, currentMonth, parseInt(day));

    // If the target day hasn't occurred this month yet, it refers to last month
    if (targetDate > now) {
      targetDate.setMonth(currentMonth - 1);
    }

    const diffTime = Math.abs(now - targetDate);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    return {
      targetDate: targetDate.toISOString(),
      currentDate: now.toISOString(),
      daysDifference: diffDays,
      targetDay: day,
      targetMonth: targetDate.toLocaleString('default', { month: 'long' }),
      targetYear: targetDate.getFullYear()
    };
  }
);

registerTool(
  'findUserDirectories',
  'Find common user directories like Documents, Desktop, Downloads, etc.',
  [],
  async () => {
    const os = require('os');
    const homeDir = os.homedir();

    const commonDirs = [
      'Documents',
      'Desktop',
      'Downloads',
      'Pictures',
      'Music',
      'Videos'
    ];

    const foundDirs = [];

    for (const dirName of commonDirs) {
      const dirPath = path.join(homeDir, dirName);
      if (await fs.pathExists(dirPath)) {
        const stats = await fs.stat(dirPath);
        foundDirs.push({
          name: dirName,
          path: dirPath,
          exists: true,
          created: stats.birthtime,
          modified: stats.mtime
        });
      } else {
        foundDirs.push({
          name: dirName,
          path: dirPath,
          exists: false
        });
      }
    }

    return {
      homeDirectory: homeDir,
      directories: foundDirs
    };
  }
);

registerTool(
  'findDirectory',
  'Search for a directory by name starting from a given path',
  [
    { name: 'directoryName', required: true, description: 'Name of the directory to find' },
    { name: 'searchPath', required: false, description: 'Path to start searching from (default: user home)' },
    { name: 'maxDepth', required: false, description: 'Maximum search depth (default: 3)' }
  ],
  async (params) => {
    const os = require('os');
    const { directoryName, searchPath = os.homedir(), maxDepth = 3 } = params;

    const searchInDir = async (dir, depth = 0) => {
      if (depth > maxDepth) return [];

      try {
        const items = await fs.readdir(dir);
        const results = [];

        for (const item of items) {
          const itemPath = path.join(dir, item);
          try {
            const stats = await fs.stat(itemPath);

            if (stats.isDirectory()) {
              if (item.toLowerCase().includes(directoryName.toLowerCase())) {
                results.push({
                  name: item,
                  path: itemPath,
                  created: stats.birthtime,
                  modified: stats.mtime,
                  depth: depth
                });
              }

              // Search recursively
              if (depth < maxDepth) {
                const subResults = await searchInDir(itemPath, depth + 1);
                results.push(...subResults);
              }
            }
          } catch (error) {
            // Skip inaccessible directories
          }
        }

        return results;
      } catch (error) {
        return [];
      }
    };

    const results = await searchInDir(searchPath);

    return {
      searchTerm: directoryName,
      searchPath,
      maxDepth,
      matches: results
    };
  }
);

// Autodesk Vault API Tools
const axios = require('axios');

// Helper function for Vault API requests
async function makeVaultRequest(endpoint, method = 'GET', data = null, accessToken = null, vaultServer = null) {
  if (!accessToken) {
    throw new Error('Access token is required for Vault API calls');
  }

  if (!vaultServer) {
    throw new Error('Vault server URL is required');
  }

  // Ensure proper base URL format for Vault API
  const baseUrl = vaultServer.endsWith('/') ? vaultServer.slice(0, -1) : vaultServer;
  const vaultApiBase = `${baseUrl}/AutodeskDM/Services/api/vault/v2`;
  const url = `${vaultApiBase}${endpoint}`;

  const config = {
    method,
    url,
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  };

  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`Vault API Error: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
    }
    throw new Error(`Vault API Request Failed: ${error.message}`);
  }
}



registerTool(
  'vaultGetFiles',
  'Get files from Autodesk Vault',
  [
    { name: 'accessToken', required: true, description: 'OAuth access token' },
    { name: 'vaultServer', required: true, description: 'Vault server URL' },
    { name: 'vaultId', required: false, description: 'Vault ID (if not provided, uses default vault)' },
    { name: 'folderId', required: false, description: 'Folder ID to get files from (root if not specified)' },
    { name: 'searchTerm', required: false, description: 'Search term to filter files' },
    { name: 'limit', required: false, description: 'Maximum number of files to return (default: 50)' }
  ],
  async (params) => {
    const { accessToken, vaultServer, vaultId, folderId, searchTerm, limit = 50 } = params;

    let endpoint = '/api/v2/files';
    const queryParams = [];

    if (vaultId) queryParams.push(`vaultId=${vaultId}`);
    if (folderId) queryParams.push(`folderId=${folderId}`);
    if (searchTerm) queryParams.push(`search=${encodeURIComponent(searchTerm)}`);
    if (limit) queryParams.push(`limit=${limit}`);

    if (queryParams.length > 0) {
      endpoint += '?' + queryParams.join('&');
    }

    const result = await makeVaultRequest(endpoint, 'GET', null, accessToken, vaultServer);

    return {
      files: result,
      searchTerm,
      folderId,
      vaultId,
      count: Array.isArray(result) ? result.length : 0
    };
  }
);



registerTool(
  'vaultGetFiles',
  'Get file versions from Autodesk Vault with optional filtering and search',
  [
    { name: 'accessToken', required: true, description: 'OAuth access token from Autodesk Platform Services' },
    { name: 'vaultServer', required: true, description: 'Vault server URL (e.g., https://your-vault-server.com)' },
    { name: 'vaultId', required: true, description: 'Vault ID to search in' },
    { name: 'searchTerm', required: false, description: 'Search term to filter files by name or properties' },
    { name: 'limit', required: false, description: 'Maximum number of file versions to return (default: 50)' }
  ],
  async (params) => {
    const { accessToken, vaultServer, vaultId, searchTerm, limit = 50 } = params;

    let endpoint = `/vaults/${vaultId}/file-versions`;
    const queryParams = [];

    if (searchTerm) queryParams.push(`q=${encodeURIComponent(searchTerm)}`);
    if (limit) queryParams.push(`limit=${limit}`);

    if (queryParams.length > 0) {
      endpoint += '?' + queryParams.join('&');
    }

    const result = await makeVaultRequest(endpoint, 'GET', null, accessToken, vaultServer);

    return {
      pagination: result.pagination || {},
      fileVersions: result.results || [],
      included: result.included || {},
      searchTerm,
      vaultId,
      count: result.results ? result.results.length : 0,
      totalResults: result.pagination ? result.pagination.totalResults : 0
    };
  }
);



app.listen(PORT, () => {
  console.log(`MCP Server running on port ${PORT}`);
  console.log(`Available endpoints:`);
  console.log(`  GET  /tools - List all available tools`);
  console.log(`  POST /tool  - Execute a specific tool`);
  console.log(`\nRegistered tools: ${Array.from(toolsRegistry.keys()).join(', ')}`);
});

#!/bin/bash

echo "Testing Autodesk Vault Integration with MCP System"
echo "=================================================="

echo ""
echo "Available Vault Tools:"
echo "---------------------"
curl -s http://localhost:3001/tools | jq '.tools[] | select(.name | startswith("vault")) | .name' | tr -d '"'

echo ""
echo "Example 1: Multi-step Vault query"
echo "---------------------------------"
echo "Question: 'How do I authenticate with my Vault server and what tools are available?'"

curl -s -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{
    "question": "What Vault tools are available and how do I authenticate with my Vault server?",
    "useMultiStep": true
  }' | jq '.response'

echo ""
echo "Example 2: Vault file search simulation"
echo "--------------------------------------"
echo "Question: 'Search for CAD files in my Vault'"

curl -s -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{
    "question": "I want to search for CAD files in my Autodesk Vault. What tools should I use and what parameters do I need?",
    "useMultiStep": true
  }' | jq '.response'

echo ""
echo "Example 3: Vault workflow planning"
echo "----------------------------------"
echo "Question: 'How can I check if a file is checked out in Vault?'"

curl -s -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{
    "question": "I need to check if a specific file is checked out in Autodesk Vault and who has it checked out. What is the workflow?",
    "useMultiStep": true
  }' | jq '.response'

echo ""
echo "Vault Integration Test Complete!"
echo ""
echo "Key Features Demonstrated:"
echo "- 13 Vault API tools available"
echo "- Multi-step planning for complex Vault workflows"
echo "- Intelligent tool selection based on user questions"
echo "- Proper API endpoint structure (/AutodeskDM/Services/api/vault/v2)"
echo "- Support for authentication, files, folders, items, search, and jobs"

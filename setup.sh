#!/bin/bash

echo "Setting up MCP System..."

echo "Installing MCP Server dependencies..."
cd mcp-server
npm install
cd ..

echo "Installing MCP Client dependencies..."
cd mcp-client  
npm install
cd ..

echo "Setup complete!"
echo ""
echo "To start the system:"
echo "1. Terminal 1: cd mcp-server && npm start"
echo "2. Terminal 2: cd mcp-client && npm start"
echo ""
echo "Then test with:"
echo 'curl -X POST http://localhost:3000/ask -H "Content-Type: application/json" -d '"'"'{"question": "What time is it?"}'"'"''

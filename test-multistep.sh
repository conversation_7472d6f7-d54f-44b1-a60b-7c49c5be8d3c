#!/bin/bash

echo "Testing Multi-Step Planning System"
echo "=================================="

echo ""
echo "1. Simple question (single step):"
curl -s -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "What time is it?", "useMultiStep": false}' | jq '.response'

echo ""
echo "2. Complex question (multi-step planning):"
curl -s -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "Find my Documents folder and list its contents", "useMultiStep": true}' | jq '.approach, .totalSteps, .response'

echo ""
echo "3. Show execution log for multi-step:"
curl -s -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "Find my Documents folder path", "useMultiStep": true}' | jq '.executionLog[].reasoning'

echo ""
echo "Multi-step planning system demonstration complete!"

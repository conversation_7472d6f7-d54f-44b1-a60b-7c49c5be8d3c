# MCP (Model Context Protocol) System

A complete MCP client-server system with GPT-4.1-nano integration for intelligent tool selection and execution.

## Architecture

- **MCP Server** (Port 3001): Hosts dynamic tools with automatic registration
- **MCP Client** (Port 3000): Provides `/ask` endpoint with AI-powered tool selection

## Features

- Dynamic tool registration - add new tools without client updates
- GPT-4.1-nano powered tool selection and response generation
- Flexible parameter system (required/optional parameters)
- Comprehensive error handling
- RESTful API design

## Quick Start

### 1. Install Dependencies

```bash
# Install server dependencies
cd mcp-server
npm install

# Install client dependencies  
cd ../mcp-client
npm install
```

### 2. Start the Services

```bash
# Terminal 1: Start MCP Server
cd mcp-server
npm start

# Terminal 2: Start MCP Client
cd mcp-client
npm start
```

### 3. Test the System

```bash
# Ask a question
curl -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "What time is it?"}'

# Complex example
curl -X POST http://localhost:3000/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "How many days ago was the Documents folder created?"}'
```

## Available Tools

The system comes with these built-in tools:

- **getTime**: Get current date and time
- **listFiles**: List files in a directory
- **getFileInfo**: Get detailed file/directory information
- **calculateDaysSince**: Calculate days between dates
- **searchFiles**: Search for files by pattern
- **readFile**: Read text file contents
- **createDirectory**: Create new directories

## API Endpoints

### MCP Client (Port 3000)

- `POST /ask` - Ask a question and get AI-powered response
- `GET /health` - Health check

### MCP Server (Port 3001)

- `GET /tools` - List all available tools
- `POST /tool` - Execute a specific tool

## Adding New Tools

Add tools to the server by calling `registerTool()`:

```javascript
registerTool(
  'toolName',
  'Tool description',
  [
    { name: 'param1', required: true, description: 'Required parameter' },
    { name: 'param2', required: false, description: 'Optional parameter' }
  ],
  async (params) => {
    // Tool implementation
    return { result: 'data' };
  }
);
```

The client will automatically discover and use new tools without any updates required.

## Configuration

Environment variables:

- `PORT` - Client port (default: 3000)
- `MCP_SERVER_URL` - Server URL (default: http://localhost:3001)

## Example Interactions

**Question**: "What date is it today?"
- Uses: `getTime` tool
- Response: Natural language response with current date/time

**Question**: "List files in my Documents folder"  
- Uses: `listFiles` tool with directory parameter
- Response: Formatted list of files with details

**Question**: "How many days ago did I create the test folder?"
- Uses: `getFileInfo` + `calculateDaysSince` tools
- Response: Natural language answer with day calculation

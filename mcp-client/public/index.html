<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Chat Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            max-height: 60vh;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.user {
            background: #3498db;
            color: white;
            margin-left: auto;
        }
        
        .message.assistant {
            background: #ecf0f1;
            color: #2c3e50;
        }
        
        .message.system {
            background: #f39c12;
            color: white;
            text-align: center;
            max-width: 100%;
        }
        
        .input-container {
            padding: 1rem;
            border-top: 1px solid #bdc3c7;
            background: #f8f9fa;
        }
        
        .input-row {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        input[type="text"], textarea {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }
        
        textarea {
            resize: vertical;
            min-height: 60px;
        }
        
        button {
            padding: 0.75rem 1.5rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
        }
        
        .chat-info {
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            padding: 0.5rem;
        }
        
        .missing-params {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 1rem;
            margin: 0.5rem 0;
        }
        
        .missing-params h4 {
            color: #856404;
            margin-bottom: 0.5rem;
        }
        
        .param-input {
            margin-bottom: 0.5rem;
        }
        
        .param-input label {
            display: block;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #495057;
        }
        
        .param-input small {
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>MCP Chat Interface</h1>
        <p>Multi-step planning with Autodesk Vault integration</p>
    </div>
    
    <div class="chat-container">
        <div class="chat-info">
            <span id="chatId">Chat ID: Not connected</span>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message system">
                Welcome! Ask me anything about files, Vault data, or general questions. I'll use multi-step planning to help you.
            </div>
        </div>
        
        <div class="input-container">
            <div id="missingParamsContainer" style="display: none;">
                <div class="missing-params">
                    <h4>Additional Information Needed</h4>
                    <div id="missingParamsContent"></div>
                    <button onclick="submitMissingParams()">Continue</button>
                </div>
            </div>

            <div id="credentialsContainer" style="display: none; margin-bottom: 1rem;">
                <div class="missing-params">
                    <h4>⚙️ Vault Credentials</h4>
                    <div class="param-input">
                        <label for="cred_accessToken">Access Token:</label>
                        <input type="text" id="cred_accessToken" placeholder="Your Vault access token" />
                    </div>
                    <div class="param-input">
                        <label for="cred_vaultServer">Vault Server:</label>
                        <input type="text" id="cred_vaultServer" placeholder="http://localhost:4000" />
                    </div>
                    <div class="param-input">
                        <label for="cred_vaultId">Vault ID:</label>
                        <input type="text" id="cred_vaultId" placeholder="117" />
                    </div>
                    <button onclick="saveCredentialsManually()">Save Credentials</button>
                    <button onclick="clearCredentials()" style="background: #e74c3c;">Clear Saved</button>
                    <button onclick="hideCredentials()" style="background: #95a5a6;">Cancel</button>
                </div>
            </div>

            <div class="input-row">
                <textarea id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)"></textarea>
                <button onclick="sendMessage()" id="sendButton">Send</button>
                <button onclick="showCredentials()" id="credButton" style="background: #f39c12;">⚙️</button>
            </div>
        </div>
    </div>

    <script>
        let currentChatId = null;
        let pendingParams = {};
        let savedCredentials = {};

        // Cookie management functions
        function setCookie(name, value, days = 30) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;
        }

        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for(let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
            }
            return null;
        }

        function deleteCookie(name) {
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        }

        // Load saved credentials from cookies
        function loadSavedCredentials() {
            const accessToken = getCookie('vaultAccessToken');
            const vaultServer = getCookie('vaultServer');
            const vaultId = getCookie('vaultId');

            if (accessToken && vaultServer && vaultId) {
                savedCredentials = {
                    accessToken: accessToken,
                    vaultServer: vaultServer,
                    vaultId: vaultId
                };

                // Show saved credentials indicator
                const chatInfo = document.querySelector('.chat-info');
                chatInfo.innerHTML += ' | <span style="color: green;">✓ Vault credentials saved</span>';

                console.log('Loaded saved Vault credentials');
            }
        }

        // Save credentials to cookies
        function saveCredentials(credentials) {
            if (credentials.accessToken) {
                setCookie('vaultAccessToken', credentials.accessToken);
                savedCredentials.accessToken = credentials.accessToken;
            }
            if (credentials.vaultServer) {
                setCookie('vaultServer', credentials.vaultServer);
                savedCredentials.vaultServer = credentials.vaultServer;
            }
            if (credentials.vaultId) {
                setCookie('vaultId', credentials.vaultId);
                savedCredentials.vaultId = credentials.vaultId;
            }

            // Update UI to show credentials are saved
            const chatInfo = document.querySelector('.chat-info');
            if (!chatInfo.innerHTML.includes('Vault credentials saved')) {
                chatInfo.innerHTML += ' | <span style="color: green;">✓ Vault credentials saved</span>';
            }
        }

        function addMessage(content, type = 'user') {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            // Format content for better display
            if (type === 'assistant' && content.includes('**')) {
                // Convert markdown-style formatting to HTML
                let formattedContent = content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/📁/g, '📁')
                    .replace(/🔒/g, '🔒')
                    .replace(/✅/g, '✅')
                    .replace(/\n/g, '<br>');

                messageDiv.innerHTML = formattedContent;
            } else {
                messageDiv.textContent = content;
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showLoading() {
            const messagesContainer = document.getElementById('chatMessages');
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading';
            loadingDiv.id = 'loadingMessage';
            loadingDiv.textContent = 'Thinking...';
            messagesContainer.appendChild(loadingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideLoading() {
            const loadingDiv = document.getElementById('loadingMessage');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        function updateChatId(chatId) {
            currentChatId = chatId;
            document.getElementById('chatId').textContent = `Chat ID: ${chatId}`;
        }

        function showMissingParams(question, missingParams) {
            const container = document.getElementById('missingParamsContainer');
            const content = document.getElementById('missingParamsContent');

            content.innerHTML = `<p><strong>${question}</strong></p>`;

            missingParams.forEach(param => {
                const paramDiv = document.createElement('div');
                paramDiv.className = 'param-input';

                // Pre-fill with saved credentials if available
                let defaultValue = '';
                const paramName = param.param.toLowerCase();
                if (paramName.includes('accesstoken') && savedCredentials.accessToken) {
                    defaultValue = savedCredentials.accessToken;
                } else if (paramName.includes('vaultserver') && savedCredentials.vaultServer) {
                    defaultValue = savedCredentials.vaultServer;
                } else if (paramName.includes('vaultid') && savedCredentials.vaultId) {
                    defaultValue = savedCredentials.vaultId;
                }

                paramDiv.innerHTML = `
                    <label for="param_${param.param}">${param.param}:</label>
                    <input type="text" id="param_${param.param}" name="${param.param}" value="${defaultValue}" />
                    <small>${param.description}</small>
                `;
                content.appendChild(paramDiv);
            });

            container.style.display = 'block';
        }

        function hideMissingParams() {
            document.getElementById('missingParamsContainer').style.display = 'none';
        }

        async function submitMissingParams() {
            const inputs = document.querySelectorAll('#missingParamsContent input');
            const userContext = {};
            const credentialsToSave = {};

            inputs.forEach(input => {
                if (input.value.trim()) {
                    userContext[input.name] = input.value.trim();

                    // Check if this is a credential that should be saved
                    const paramName = input.name.toLowerCase();
                    if (paramName.includes('accesstoken')) {
                        credentialsToSave.accessToken = input.value.trim();
                    } else if (paramName.includes('vaultserver')) {
                        credentialsToSave.vaultServer = input.value.trim();
                    } else if (paramName.includes('vaultid')) {
                        credentialsToSave.vaultId = input.value.trim();
                    }
                }
            });

            // Save credentials to cookies
            if (Object.keys(credentialsToSave).length > 0) {
                saveCredentials(credentialsToSave);
                addMessage("✓ Vault credentials saved for future use", 'system');
            }

            hideMissingParams();

            // Continue with the previous question using the new context
            await sendMessage("Continue with the provided information", userContext);
        }

        async function sendMessage(messageText = null, userContext = {}) {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');

            const message = messageText || input.value.trim();
            if (!message) return;

            if (!messageText) {
                addMessage(message, 'user');
                input.value = '';
            }

            // Merge saved credentials with provided userContext
            const combinedContext = { ...savedCredentials, ...userContext };

            sendButton.disabled = true;
            showLoading();

            try {
                const response = await fetch('/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: message,
                        chatId: currentChatId,
                        userContext: combinedContext,
                        useMultiStep: true
                    })
                });
                
                const data = await response.json();
                hideLoading();
                
                if (data.chatId) {
                    updateChatId(data.chatId);
                }
                
                if (data.needsUserInput) {
                    addMessage("I need some additional information to continue:", 'assistant');
                    showMissingParams(data.userQuestion, data.missingParams);
                } else if (data.response) {
                    addMessage(data.response, 'assistant');
                }
                
                if (data.error) {
                    addMessage(`Error: ${data.error}`, 'system');
                }
                
            } catch (error) {
                hideLoading();
                addMessage(`Error: ${error.message}`, 'system');
            }
            
            sendButton.disabled = false;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Credential management functions
        function showCredentials() {
            const container = document.getElementById('credentialsContainer');

            // Pre-fill with current saved credentials
            document.getElementById('cred_accessToken').value = savedCredentials.accessToken || '';
            document.getElementById('cred_vaultServer').value = savedCredentials.vaultServer || '';
            document.getElementById('cred_vaultId').value = savedCredentials.vaultId || '';

            container.style.display = 'block';
        }

        function hideCredentials() {
            document.getElementById('credentialsContainer').style.display = 'none';
        }

        function saveCredentialsManually() {
            const accessToken = document.getElementById('cred_accessToken').value.trim();
            const vaultServer = document.getElementById('cred_vaultServer').value.trim();
            const vaultId = document.getElementById('cred_vaultId').value.trim();

            const credentialsToSave = {};
            if (accessToken) credentialsToSave.accessToken = accessToken;
            if (vaultServer) credentialsToSave.vaultServer = vaultServer;
            if (vaultId) credentialsToSave.vaultId = vaultId;

            if (Object.keys(credentialsToSave).length > 0) {
                saveCredentials(credentialsToSave);
                addMessage("✓ Vault credentials saved successfully", 'system');
                hideCredentials();
            } else {
                addMessage("⚠️ Please enter at least one credential", 'system');
            }
        }

        function clearCredentials() {
            deleteCookie('vaultAccessToken');
            deleteCookie('vaultServer');
            deleteCookie('vaultId');
            savedCredentials = {};

            // Update UI
            const chatInfo = document.querySelector('.chat-info');
            chatInfo.innerHTML = chatInfo.innerHTML.replace(' | <span style="color: green;">✓ Vault credentials saved</span>', '');

            addMessage("🗑️ Vault credentials cleared", 'system');
            hideCredentials();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedCredentials();
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>

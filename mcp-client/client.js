const express = require('express');
const cors = require('cors');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3001';
const GPT_API_URL = 'https://api.epicai.fun/v1/chat/completions';
const GPT_API_KEY = 'ecs-00';

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Chat session storage
const chatSessions = new Map();

async function fetchAvailableTools() {
  try {
    const response = await axios.get(`${MCP_SERVER_URL}/tools`);
    return response.data.tools;
  } catch (error) {
    throw new Error(`Failed to fetch tools from MCP server: ${error.message}`);
  }
}

function parseStreamingResponse(streamData) {
  const lines = streamData.split('\n');
  let content = '';

  for (const line of lines) {
    if (line.startsWith('data: ') && !line.includes('[DONE]')) {
      try {
        const jsonStr = line.substring(6);
        const data = JSON.parse(jsonStr);
        if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
          content += data.choices[0].delta.content;
        }
      } catch (e) {
        // Skip invalid JSON lines
      }
    }
  }

  return content;
}

async function callGPT(messages) {
  try {
    const response = await axios.post(GPT_API_URL, {
      model: 'gpt-4.1-nano',
      messages: messages,
      temperature: 0.1,
      stream: false
    }, {
      headers: {
        'Authorization': `Bearer ${GPT_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    // Handle both streaming and non-streaming responses
    if (typeof response.data === 'string' && response.data.includes('data: ')) {
      // This is a streaming response
      return parseStreamingResponse(response.data);
    } else if (response.data && response.data.choices && response.data.choices[0]) {
      // This is a regular JSON response
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`Invalid GPT API response structure: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    if (error.response) {
      console.error('GPT API Error Response:', error.response.data);
      throw new Error(`GPT API call failed: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
    }
    throw new Error(`GPT API call failed: ${error.message}`);
  }
}

async function executeToolOnServer(toolName, params) {
  try {
    const response = await axios.post(`${MCP_SERVER_URL}/tool`, {
      toolName,
      params
    });
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`Tool execution failed: ${error.response.data.error}`);
    }
    throw new Error(`Failed to execute tool: ${error.message}`);
  }
}

function createToolSelectionPrompt(question, tools) {
  const toolDescriptions = tools.map(tool => {
    const paramsDesc = tool.params.map(p => 
      `${p.name} (${p.required ? 'required' : 'optional'}): ${p.description}`
    ).join(', ');
    
    return `- ${tool.name}: ${tool.description}${paramsDesc ? ` | Parameters: ${paramsDesc}` : ''}`;
  }).join('\n');

  return `You are a tool selection assistant. Given a user question and available tools, determine which tool(s) to use and what parameters to provide.

Available tools:
${toolDescriptions}

User question: "${question}"

Respond with a JSON array of tools to use. Each tool should have:
- toolName: the exact name of the tool
- params: object with parameter values (only include parameters that are needed)

If multiple tools are needed, include them all in the array. If no tools are needed, return an empty array.

Example response format:
[
  {
    "toolName": "getTime",
    "params": {}
  },
  {
    "toolName": "listFiles", 
    "params": {
      "directory": "/path/to/dir",
      "includeHidden": false
    }
  }
]

Response:`;
}

function createResponsePrompt(question, toolResults) {
  const resultsText = toolResults.map(result => 
    `Tool: ${result.toolName}\nParameters: ${JSON.stringify(result.params)}\nResult: ${JSON.stringify(result.result)}`
  ).join('\n\n');

  return `You are a helpful assistant. A user asked a question and we executed some tools to gather information. Please provide a natural, helpful response based on the tool results.

User question: "${question}"

Tool execution results:
${resultsText}

Please provide a clear, natural response that answers the user's question based on the tool results:`;
}

async function executeMultiStepPlan(question, tools, maxSteps = 5, chatId = null, userContext = {}) {
  const executionLog = [];
  let currentContext = `Original question: "${question}"`;
  let stepCount = 0;

  // Include user context if available
  if (userContext && Object.keys(userContext).length > 0) {
    currentContext += `\n\nUser context: ${JSON.stringify(userContext)}`;
  }

  while (stepCount < maxSteps) {
    stepCount++;

    // Create planning prompt
    const planningPrompt = `You are an AI assistant that can break down complex tasks into steps and execute tools to gather information.

${currentContext}

Available tools:
${tools.map(tool => {
  const paramsDesc = tool.params.map(p =>
    `${p.name} (${p.required ? 'required' : 'optional'}): ${p.description}`
  ).join(', ');
  return `- ${tool.name}: ${tool.description}${paramsDesc ? ` | Parameters: ${paramsDesc}` : ''}`;
}).join('\n')}

TOOL SELECTION GUIDELINES:
- For "vault files", "Autodesk Vault", or "Vault" requests with accessToken/vaultServer/vaultId in context, use "vaultGetFiles"
- For local file system operations, use "listFiles", "searchFiles", etc.
- For user directory discovery, use "findUserDirectories"

Previous execution steps:
${executionLog.map((step, i) => `Step ${i + 1}: ${step.description}\nResult: ${JSON.stringify(step.results)}`).join('\n\n')}

Analyze the situation and decide what to do next. You MUST respond with valid JSON only, no additional text.

IMPORTANT GUIDELINES:
- For simple greetings, casual conversation, or questions that don't require tools, use "complete" action
- If you have executed tools and retrieved data that answers the user's question, use "complete" action with formatted results
- Only use "ask_user" when you need specific technical information (like API tokens, file paths, etc.) to execute tools
- Only use "execute" when you have all required parameters to run tools
- Don't ask for additional information unless it's absolutely necessary to fulfill a specific technical request
- If you have file lists, search results, or other data from previous steps, format it nicely and use "complete"

1. If you have enough information to answer the original question OR if it's a simple greeting/conversation OR if you have data from previous tool executions, respond with:
   {"action": "complete", "answer": "your final answer here"}

2. If you need to execute tools and have all required parameters, respond with:
   {"action": "execute", "tools": [{"toolName": "toolName", "params": {}, "description": "why you're using this tool"}], "reasoning": "explain what you're trying to find out"}

3. If you need information from the user (missing required technical parameters for tools), respond with:
   {"action": "ask_user", "question": "What specific technical information do you need?", "missingParams": [{"param": "paramName", "description": "what this parameter is for"}]}

4. If the question cannot be answered with available tools, respond with:
   {"action": "impossible", "reason": "explanation of why it cannot be answered"}

IMPORTANT: Respond with ONLY the JSON object, no other text or formatting.`;

    const planningResponse = await callGPT([
      { role: 'user', content: planningPrompt }
    ]);

    console.log('Planning response received:', planningResponse);

    let planningDecision;
    try {
      // Clean up the response to extract JSON
      let cleanResponse = planningResponse.trim();

      // Try multiple JSON extraction methods
      let jsonStr = null;

      // Method 1: Look for complete JSON object
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonStr = jsonMatch[0];
      }

      // Method 2: If no JSON found, try to extract from code blocks
      if (!jsonStr) {
        const codeBlockMatch = cleanResponse.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
        if (codeBlockMatch) {
          jsonStr = codeBlockMatch[1];
        }
      }

      // Method 3: If still no JSON, check if the entire response is JSON-like
      if (!jsonStr && cleanResponse.startsWith('{') && cleanResponse.endsWith('}')) {
        jsonStr = cleanResponse;
      }

      // If we still don't have JSON, create a fallback response
      if (!jsonStr) {
        console.warn('No JSON found in planning response, creating fallback:', planningResponse);

        // For simple greetings or questions that don't need tools, just return the response
        if (planningResponse && planningResponse.trim()) {
          planningDecision = {
            action: "complete",
            answer: planningResponse.trim()
          };
        } else {
          planningDecision = {
            action: "complete",
            answer: "Hello! I'm here to help you with files, Vault data, and other questions. What would you like to know?"
          };
        }
      } else {
        planningDecision = JSON.parse(jsonStr);
      }

      // Debug: Log the planning decision
      console.log('Planning decision:', JSON.stringify(planningDecision, null, 2));

    } catch (parseError) {
      console.error('Planning response parsing failed:', planningResponse);
      console.error('Parse error:', parseError.message);

      // If the response looks like it might be a direct answer, use it
      if (planningResponse && planningResponse.trim() && !planningResponse.includes('"action"')) {
        planningDecision = {
          action: "complete",
          answer: planningResponse.trim()
        };
      } else {
        // Create a fallback response
        planningDecision = {
          action: "complete",
          answer: "I apologize, but I encountered an issue processing your request. Could you please try rephrasing your question?"
        };
      }
    }

    if (planningDecision.action === 'complete') {
      return {
        success: true,
        answer: planningDecision.answer,
        executionLog,
        totalSteps: stepCount,
        chatId
      };
    }

    if (planningDecision.action === 'ask_user') {
      return {
        success: false,
        needsUserInput: true,
        question: planningDecision.question,
        missingParams: planningDecision.missingParams || [],
        executionLog,
        totalSteps: stepCount,
        chatId
      };
    }

    if (planningDecision.action === 'impossible') {
      return {
        success: false,
        reason: planningDecision.reason,
        executionLog,
        totalSteps: stepCount,
        chatId
      };
    }

    if (planningDecision.action === 'execute') {
      // Check if tools have missing required parameters
      const missingParams = [];
      for (const toolPlan of planningDecision.tools) {
        const tool = tools.find(t => t.name === toolPlan.toolName);
        if (tool) {
          const requiredParams = tool.params.filter(p => p.required);
          for (const param of requiredParams) {
            if (!(param.name in toolPlan.params) || !toolPlan.params[param.name]) {
              missingParams.push({
                toolName: toolPlan.toolName,
                param: param.name,
                description: param.description
              });
            }
          }
        }
      }

      if (missingParams.length > 0) {
        return {
          success: false,
          needsUserInput: true,
          question: `I need some additional information to proceed:`,
          missingParams,
          executionLog,
          totalSteps: stepCount,
          chatId
        };
      }

      // Execute the planned tools
      const stepResults = [];

      for (const toolPlan of planningDecision.tools) {
        try {
          console.log(`Executing tool: ${toolPlan.toolName} with params:`, toolPlan.params);
          const result = await executeToolOnServer(toolPlan.toolName, toolPlan.params);
          console.log(`Tool result:`, result);
          stepResults.push({
            toolName: toolPlan.toolName,
            params: toolPlan.params,
            result: result.result,
            description: toolPlan.description
          });
        } catch (toolError) {
          console.error(`Tool execution error for ${toolPlan.toolName}:`, toolError.message);
          stepResults.push({
            toolName: toolPlan.toolName,
            params: toolPlan.params,
            error: toolError.message,
            description: toolPlan.description
          });
        }
      }

      // Add to execution log
      executionLog.push({
        step: stepCount,
        reasoning: planningDecision.reasoning,
        tools: planningDecision.tools,
        results: stepResults,
        description: planningDecision.reasoning
      });

      // Check if we got Vault file results that should be formatted and returned
      const vaultFileResults = stepResults.find(r => r.toolName === 'vaultGetFiles' && r.result && r.result.fileVersions);
      if (vaultFileResults && vaultFileResults.result.fileVersions.length > 0) {
        const fileList = vaultFileResults.result.fileVersions
          .map((file, index) => `${index + 1}. ${file.name}`)
          .join('\n');

        return {
          success: true,
          answer: `Here are all your Vault files (${vaultFileResults.result.count} files):\n\n${fileList}`,
          executionLog,
          totalSteps: stepCount,
          chatId
        };
      }

      // Check if we got search results that should be formatted and returned
      const searchResults = stepResults.find(r => r.toolName === 'searchFiles' && r.result && r.result.matches);
      if (searchResults && searchResults.result.matches.length > 0) {
        const resultList = searchResults.result.matches
          .map(match => `- ${match.name} (${match.path})`)
          .join('\n');

        return {
          success: true,
          answer: `Found ${searchResults.result.matches.length} matches:\n\n${resultList}`,
          executionLog,
          totalSteps: stepCount,
          chatId
        };
      } else if (searchResults && searchResults.result.matches.length === 0) {
        return {
          success: true,
          answer: `No files found matching your search criteria.`,
          executionLog,
          totalSteps: stepCount,
          chatId
        };
      }

      // Update context for next iteration
      currentContext = `Original question: "${question}"

Current findings:
${executionLog.map(step => `- ${step.reasoning}: ${step.results.map(r => r.error ? `Error: ${r.error}` : JSON.stringify(r.result)).join(', ')}`).join('\n')}

IMPORTANT: If you have retrieved data that answers the user's question (like file lists, search results, etc.), you should use "complete" action and provide a formatted answer to the user. Do not ask for further clarification when you have the requested data.`;
    }
  }

  // If we reach here, we've hit max steps - try to provide a helpful summary
  const summaryPrompt = `Based on the execution steps below, provide a helpful summary of what was found:

Original question: "${question}"

Execution steps:
${executionLog.map(step => `- ${step.reasoning}: ${step.results.map(r => r.error ? `Error: ${r.error}` : JSON.stringify(r.result)).join(', ')}`).join('\n')}

Please provide a clear, helpful response based on the information gathered:`;

  try {
    const summaryResponse = await callGPT([
      { role: 'user', content: summaryPrompt }
    ]);

    return {
      success: true,
      answer: summaryResponse,
      executionLog,
      totalSteps: stepCount,
      chatId
    };
  } catch (error) {
    return {
      success: false,
      reason: `Reached maximum steps (${maxSteps}) without completing the task`,
      executionLog,
      totalSteps: stepCount,
      chatId
    };
  }
}

app.post('/ask', async (req, res) => {
  try {
    const { question, useMultiStep = true, chatId, userContext = {} } = req.body;

    if (!question) {
      return res.status(400).json({ error: 'Question is required' });
    }

    // Generate or use existing chat ID
    const sessionId = chatId || uuidv4();

    // Get or create chat session
    if (!chatSessions.has(sessionId)) {
      chatSessions.set(sessionId, {
        id: sessionId,
        messages: [],
        context: {},
        created: new Date().toISOString()
      });
    }

    const session = chatSessions.get(sessionId);

    // Add user message to session
    session.messages.push({
      role: 'user',
      content: question,
      timestamp: new Date().toISOString()
    });

    // Merge user context with session context
    const combinedContext = { ...session.context, ...userContext };

    // Check for simple greetings or casual conversation
    const simpleGreetings = /^(hi|hello|hey|good morning|good afternoon|good evening|how are you|what's up|sup)[\s\?\!]*$/i;
    if (simpleGreetings.test(question.trim())) {
      const greetingResponse = "Hello! I'm here to help you with files, Vault data, and other questions. What would you like to know or do?";

      session.messages.push({
        role: 'assistant',
        content: greetingResponse,
        timestamp: new Date().toISOString()
      });

      return res.json({
        chatId: sessionId,
        question,
        response: greetingResponse,
        approach: 'simple-greeting'
      });
    }

    const tools = await fetchAvailableTools();

    if (useMultiStep) {
      // Use multi-step planning approach
      const result = await executeMultiStepPlan(question, tools, 5, sessionId, combinedContext);

      if (result.success) {
        // Add assistant response to session
        session.messages.push({
          role: 'assistant',
          content: result.answer,
          timestamp: new Date().toISOString()
        });

        return res.json({
          chatId: sessionId,
          question,
          response: result.answer,
          executionLog: result.executionLog,
          totalSteps: result.totalSteps,
          approach: 'multi-step'
        });
      } else if (result.needsUserInput) {
        // Store current state in session for continuation
        session.context.pendingExecution = {
          executionLog: result.executionLog,
          totalSteps: result.totalSteps,
          originalQuestion: question
        };

        return res.json({
          chatId: sessionId,
          question,
          needsUserInput: true,
          userQuestion: result.question,
          missingParams: result.missingParams,
          executionLog: result.executionLog,
          totalSteps: result.totalSteps,
          approach: 'multi-step'
        });
      } else {
        // Add assistant response to session
        session.messages.push({
          role: 'assistant',
          content: `I couldn't complete this task. ${result.reason}`,
          timestamp: new Date().toISOString()
        });

        return res.json({
          chatId: sessionId,
          question,
          response: `I couldn't complete this task. ${result.reason}`,
          executionLog: result.executionLog,
          totalSteps: result.totalSteps,
          approach: 'multi-step'
        });
      }
    }

    // Fallback to original single-step approach
    const toolSelectionPrompt = createToolSelectionPrompt(question, tools);
    const toolSelectionResponse = await callGPT([
      { role: 'user', content: toolSelectionPrompt }
    ]);

    let selectedTools;
    try {
      selectedTools = JSON.parse(toolSelectionResponse);
    } catch (parseError) {
      throw new Error(`Failed to parse tool selection response: ${toolSelectionResponse}`);
    }

    if (!Array.isArray(selectedTools)) {
      throw new Error('Tool selection response must be an array');
    }

    const toolResults = [];
    for (const toolCall of selectedTools) {
      try {
        const result = await executeToolOnServer(toolCall.toolName, toolCall.params);
        toolResults.push(result);
      } catch (toolError) {
        toolResults.push({
          toolName: toolCall.toolName,
          params: toolCall.params,
          error: toolError.message
        });
      }
    }

    if (toolResults.length === 0) {
      const directResponse = await callGPT([
        { role: 'user', content: `Please respond to this question: "${question}"` }
      ]);

      return res.json({
        question,
        response: directResponse,
        toolsUsed: [],
        toolResults: [],
        approach: 'single-step'
      });
    }

    const responsePrompt = createResponsePrompt(question, toolResults);
    const finalResponse = await callGPT([
      { role: 'user', content: responsePrompt }
    ]);

    res.json({
      question,
      response: finalResponse,
      toolsUsed: selectedTools,
      toolResults,
      approach: 'single-step'
    });

  } catch (error) {
    console.error('Error processing request:', error);
    res.status(500).json({
      error: error.message,
      question: req.body.question
    });
  }
});

app.get('/chat/:chatId', (req, res) => {
  const { chatId } = req.params;

  if (!chatSessions.has(chatId)) {
    return res.status(404).json({ error: 'Chat session not found' });
  }

  const session = chatSessions.get(chatId);
  res.json(session);
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    mcpServerUrl: MCP_SERVER_URL,
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`MCP Client running on port ${PORT}`);
  console.log(`Connected to MCP Server: ${MCP_SERVER_URL}`);
  console.log(`Available endpoints:`);
  console.log(`  POST /ask   - Ask a question and get AI-powered response`);
  console.log(`  GET  /health - Health check`);
});
